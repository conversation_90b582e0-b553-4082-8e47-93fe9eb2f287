import { getTranslation } from '@/lib/i18n';

export default function Gallery({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const images = [
    {
      title: 'luffy',
      url: '/gallery/luffy.jpg',
      arturl: '/gallery/luffy-Art.png'
    },
    {
      title: 'zoro',
      url: '/gallery/zoro.jpeg',
      arturl: '/gallery/zoro-Art.png'
    },
    {
      title: 'Sanji',
      url: '/gallery/Sanji.jpeg',
      arturl: '/gallery/Sanji-Art.png'
    },
    {
      title: 'Brook',
      url: '/gallery/Brook.jpg',
      arturl: '/gallery/Brook-Art.png'
    },
    {
      title: '<PERSON><PERSON>',
      url: '/gallery/Franky.jpg',
      arturl: '/gallery/Franky-Art.png'
    },
    {
      title: 'Jinbe',
      url: '/gallery/Jinbe.png',
      arturl: '/gallery/Jinbe-Art.png'
    },
    {
      title: '<PERSON><PERSON>',
      url: '/gallery/Nami.webp',
      arturl: '/gallery/Nami-Art.png'
    },
    {
      title: '<PERSON>',
      url: '/gallery/Robin.webp',
      arturl: '/gallery/Robin-Art.png'
    },
    {
      title: 'Usopp',
      url: '/gallery/Usopp.webp',
      arturl: '/gallery/Usopp-Art.png'
    },
  ]

  return (
    <>
      <h2 className="text-center text-2xl font-bold px-2 py-4">{t('What is String Art Generator?')}</h2>
      <div className="md:p-10">
        <div className="flex flex-wrap gap-6 justify-center">
          {images.map((image, index) => (
            <div key={index} className="flex flex-col items-center">
              <img
                src={image.arturl}
                alt={image.title}
                className="w-[280px] h-[280px] object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-4"
              />
            </div>
          ))}
        </div>
      </div>
    </>
  )
}
