"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  useDisclosure
} from "@heroui/react";
import { RiMenuLine } from "@remixicon/react";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import { getTranslation } from "@/lib/i18n";
import { getLinkHref } from "@/lib/hreflang";
import { ThemeSwitcher } from "./ThemeSwitcher";

export default function MobileMenu({ locale }) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const t = function (key) {
    return getTranslation(locale, key);
  }

  const handleLinkClick = () => {
    onOpenChange(false);
  };

  return (
    <div className="mx-auto items-center justify-between h-14 flex lg:hidden">
      <Button isIconOnly variant="light" onPress={onOpen}>
        <RiMenuLine className="w-6 h-6" />
      </Button>
      <Drawer isOpen={isOpen} onOpen<PERSON>hang<PERSON>={onO<PERSON>Change} placement="right" className="w-4/5">
        <DrawerContent>
          <DrawerBody>
            <div className="flex flex-col gap-4 p-4">
              <Link href={getLinkHref(locale, "")} className="text-lg text-foreground hover:text-primary flex items-center" onClick={handleLinkClick}>
                <img src="/images/logo.png" alt="StringArtGenerator" width={32} height={32} />
                <p className="font-bold text-inherit mx-3 text-2xl">
                  {t('String Art Generator')}
                </p>
              </Link>
              <Link href="#key-features" className="text-lg text-foreground hover:text-primary" onClick={handleLinkClick}>
                {t('Key Features')}
              </Link>
              <Link href="#how-to-use" className="text-lg text-foreground hover:text-primary" onClick={handleLinkClick}>
                {t('How To Use')}
              </Link>
              <Link href="#use-case" className="text-lg text-foreground hover:text-primary" onClick={handleLinkClick}>
                {t('Feedback')}
              </Link>
              <Link href="#faq" className="text-lg text-foreground hover:text-primary" onClick={handleLinkClick}>
                {t('FAQ')}
              </Link>
              <Link href={getLinkHref(locale, "blog")} className="text-lg text-foreground hover:text-primary" onClick={handleLinkClick}>
                {t('Blog')}
              </Link>
              <div className="flex items-center gap-4">
                <LanguageSwitcher locale={locale} />
                <ThemeSwitcher></ThemeSwitcher>
              </div>
            </div>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </div>
  );
}
